<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师管理后台 - 班级成绩管理系统</title>

    <!-- 统一样式 -->
    <link rel="stylesheet" href="/css/style.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <style>
        /* 教师管理界面专用样式 - 全屏布局 */
        body {
            background-color: #f0f4f8;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(74, 144, 226, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(103, 184, 255, 0.05) 0%, transparent 50%);
            color: #333;
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
            min-height: 100vh;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        /* 主容器样式 - 全屏布局 */
        .teacher-container {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: none;
            border-radius: 0;
            box-shadow: none;
            width: 100%;
            height: 100vh;
            margin: 0;
            overflow: hidden;
            display: flex;
        }

        /* 侧边栏样式 - 全屏适配 */
        .sidebar {
            background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%);
            width: 280px;
            padding: 0;
            border-radius: 0;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 30px 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 600;
            color: white;
            font-size: 22px;
        }

        .sidebar-header small {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-link {
            color: white;
            padding: 15px 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .nav-link i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        .nav-link:hover,
        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateX(5px);
            box-shadow: inset 4px 0 0 rgba(255, 255, 255, 0.5);
        }

        /* 主内容区域 - 全屏适配 */
        .content-area {
            flex: 1;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 0;
            height: 100vh;
            overflow-y: auto;
        }

        .top-navbar {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 20px 30px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .top-navbar h5 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }

        /* 页面头部 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 25px 30px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .page-header h2 {
            margin: 0;
            color: #333;
            font-weight: 600;
            font-size: 28px;
        }

        .page-header h2 i {
            margin-right: 12px;
            color: #4a90e2;
        }

        /* 统计卡片 */
        .stats-card {
            background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-card.bg-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .stats-card.bg-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
        }

        .stats-card.bg-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
            color: #333;
        }

        .stat-icon {
            font-size: 3rem;
            margin-right: 20px;
            opacity: 0.9;
        }

        .stat-content h3 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .stat-content p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }

        /* 卡片样式 */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            margin-bottom: 25px;
        }

        .card:hover {
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .card-header {
            background: rgba(74, 144, 226, 0.1);
            border-bottom: 1px solid rgba(74, 144, 226, 0.2);
            border-radius: 15px 15px 0 0 !important;
            padding: 20px 25px;
        }

        .card-header h5 {
            margin: 0;
            color: #333;
            font-weight: 600;
            font-size: 18px;
        }

        .card-header h5 i {
            margin-right: 10px;
            color: #4a90e2;
        }

        .card-body {
            padding: 25px;
        }

        /* 快速操作按钮 */
        .quick-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .quick-actions .btn {
            border-radius: 12px;
            padding: 15px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            min-width: 140px;
        }

        .quick-actions .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .quick-actions .btn-primary {
            background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%);
        }

        .quick-actions .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .quick-actions .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .quick-actions .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        /* 表格样式 */
        .table {
            margin: 0;
            border-radius: 10px;
            overflow: hidden;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: #333;
            background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%);
            color: white;
            padding: 15px;
        }

        .table td {
            padding: 15px;
            vertical-align: middle;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .table tbody tr:hover {
            background-color: rgba(74, 144, 226, 0.05);
        }

        /* 按钮样式 */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 14px;
        }

        .btn-outline-primary {
            border: 1px solid #4a90e2;
            color: #4a90e2;
        }

        .btn-outline-primary:hover {
            background: #4a90e2;
            color: white;
        }

        .btn-outline-danger {
            border: 1px solid #dc3545;
            color: #dc3545;
        }

        .btn-outline-danger:hover {
            background: #dc3545;
            color: white;
        }

        /* 表单样式 */
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid rgba(74, 144, 226, 0.3);
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #4a90e2;
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        /* 模态框样式 */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px 25px;
        }

        .modal-title {
            font-weight: 600;
        }

        .modal-body {
            padding: 25px;
        }

        .modal-footer {
            padding: 20px 25px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* 消息容器 */
        #message-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
        }

        /* 徽章样式 */
        .badge {
            border-radius: 6px;
            padding: 6px 10px;
            font-weight: 500;
        }

        /* 强制隐藏所有模态框背景遮罩 */
        .modal-backdrop {
            display: none !important;
        }

        /* 确保没有任何遮罩层影响页面 */
        .modal-backdrop.fade,
        .modal-backdrop.show,
        .modal-backdrop.fade.show {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
        }

        /* 批量操作按钮样式 */
        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .quick-actions .btn {
            min-width: 120px;
        }

        /* 表格复选框样式 */
        .grade-checkbox {
            transform: scale(1.2);
            margin: 0;
        }

        /* 批量操作提示 */
        .batch-info {
            background: rgba(13, 110, 253, 0.1);
            border: 1px solid rgba(13, 110, 253, 0.2);
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #0d6efd;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .teacher-container {
                flex-direction: column;
                width: 98%;
            }

            .sidebar {
                width: 100%;
                border-radius: 16px 16px 0 0;
            }

            .content-area {
                border-radius: 0 0 16px 16px;
                padding: 20px;
            }

            .quick-actions {
                flex-direction: column;
            }

            .stats-card {
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 消息容器 -->
    <div id="message-container"></div>

    <!-- 主容器 -->
    <div class="teacher-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h4><i class="fas fa-graduation-cap"></i> 教师管理</h4>
                <small>班级成绩管理系统</small>
            </div>
            <nav class="sidebar-nav">
                <a href="#" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台预览
                </a>
                <a href="#" class="nav-link" data-section="schools">
                    <i class="fas fa-school"></i>
                    学校管理
                </a>
                <a href="#" class="nav-link" data-section="grades">
                    <i class="fas fa-layer-group"></i>
                    年级管理
                </a>
                <a href="#" class="nav-link" data-section="students">
                    <i class="fas fa-users"></i>
                    学生管理
                </a>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="content-area">
            <!-- 顶部导航栏 -->
            <div class="top-navbar">
                <div>
                    <h5 class="mb-0">欢迎回来，<span id="user-name">教师</span></h5>
                </div>
                <div>
                    <button class="btn btn-outline-danger btn-sm" id="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> 退出登录
                    </button>
                </div>
            </div>

            <!-- 内容区域 -->
            <div id="content-area">
                <!-- 动态内容将在这里显示 -->
            </div>
        </div>
    </div>



    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SheetJS for Excel processing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- 配置文件 -->
    <script src="/js/config.js"></script>
    <!-- 教师管理脚本 -->
    <script src="/js/teacher-management-new.js"></script>
</body>
</html>
