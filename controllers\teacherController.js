/**
 * 教师权限管理控制器
 * 实现教师只能看到自己数据的权限控制
 */

const db = require('../config/db');
const cache = require('../utils/cache');

/**
 * 检查教师是否有访问指定学校的权限
 */
async function checkTeacherSchoolPermission(teacherId, schoolId) {
  const { data, error } = await db.supabase
    .from('teacher_school_assignments')
    .select('*')
    .eq('teacher_id', teacherId)
    .eq('school_id', schoolId)
    .single();

  return !error && data;
}

/**
 * 确保教师有指定学校的分配权限（如果没有则自动创建）
 */
async function ensureTeacherSchoolAssignment(teacherId, schoolId) {
  try {
    // 首先检查是否已存在
    const { data: existingAssignment } = await db.supabase
      .from('teacher_school_assignments')
      .select('id')
      .eq('teacher_id', teacherId)
      .eq('school_id', schoolId)
      .single();

    if (existingAssignment) {
      return { success: true, existed: true };
    }

    // 如果不存在，创建新的分配
    const { data: newAssignment, error } = await db.supabase
      .from('teacher_school_assignments')
      .insert({
        teacher_id: teacherId,
        school_id: schoolId,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      if (error.code === '23505') {
        // 重复插入，可能是并发操作导致的
        return { success: true, existed: true };
      }
      console.error('创建教师学校分配失败:', error);
      return { success: false, error: error.message };
    }

    console.log(`成功为教师 ${teacherId} 创建学校 ${schoolId} 的分配权限`);
    return { success: true, existed: false, data: newAssignment };
  } catch (error) {
    console.error('确保教师学校分配失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 检查教师是否有访问指定班级的权限
 */
async function checkTeacherClassPermission(teacherId, schoolId, grade, classNum) {
  const { data, error } = await db.supabase
    .from('teacher_class_permissions')
    .select('*')
    .eq('teacher_id', teacherId)
    .eq('school_id', schoolId)
    .eq('grade', grade)
    .eq('class', classNum)
    .single();
  
  return !error && data;
}

/**
 * 获取教师可访问的所有学校
 */
async function getTeacherSchools(req, res) {
  try {
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';
    const cacheKey = `schools_${isAdmin ? 'admin' : teacherId}`;

    // 尝试从缓存获取
    const cached = cache.get(cacheKey);
    if (cached) {
      return res.json(cached);
    }

    let query;
    if (isAdmin) {
      // 管理员可以看到所有学校 - 只查询必要字段
      query = db.supabase
        .from('schools')
        .select('id, name, address, contact_phone, created_at')
        .order('created_at', { ascending: false })
        .limit(100);
    } else {
      // 教师只能看到有权限的学校
      query = db.supabase
        .from('teacher_permissions_view')
        .select('school_id, school_name')
        .eq('teacher_id', teacherId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('获取教师学校数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取学校数据失败'
      });
    }

    let result;
    if (isAdmin) {
      result = data || [];
    } else {
      // 转换数据格式并去重
      const schoolMap = new Map();
      data?.forEach(item => {
        if (!schoolMap.has(item.school_id)) {
          schoolMap.set(item.school_id, {
            id: item.school_id,
            name: item.school_name
          });
        }
      });
      result = Array.from(schoolMap.values());
    }

    // 缓存结果
    cache.set(cacheKey, result, isAdmin ? 10 * 60 * 1000 : 5 * 60 * 1000);
    res.json(result);
  } catch (error) {
    console.error('获取教师学校数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 获取教师可访问的所有学生
 */
async function getTeacherStudents(req, res) {
  try {
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (isAdmin) {
      // 管理员查询 - 只查询必要字段，限制数量
      const { data, error } = await db.supabase
        .from('students')
        .select(`
          id,
          student_identifier,
          name,
          grade,
          class,
          school_id,
          status,
          schools!inner(name)
        `)
        .order('school_id, grade, class')
        .limit(1000);

      if (error) throw error;
      res.json(data || []);
      return;
    }

    // 教师查询 - 使用优化的单次查询
    const { data, error } = await db.supabase
      .from('teacher_permissions_view')
      .select(`
        student_id,
        student_identifier,
        student_name,
        grade,
        class,
        school_id,
        school_name
      `)
      .eq('teacher_id', teacherId)
      .order('school_id, grade, class')
      .limit(500);

    if (error) {
      // 如果视图不存在，回退到原始查询
      const { data: permissions } = await db.supabase
        .from('teacher_class_permissions')
        .select('school_id, grade, class')
        .eq('teacher_id', teacherId);

      if (!permissions?.length) {
        return res.json([]);
      }

      const orConditions = permissions.map(p =>
        `and(school_id.eq.${p.school_id},grade.eq.${p.grade},class.eq.${p.class})`
      ).join(',');

      const { data: students, error: studentsError } = await db.supabase
        .from('students')
        .select(`
          id,
          student_identifier,
          name,
          grade,
          class,
          school_id,
          schools!inner(name)
        `)
        .or(orConditions)
        .order('school_id, grade, class')
        .limit(500);

      if (studentsError) throw studentsError;
      return res.json(students || []);
    }

    // 转换视图数据格式
    const formattedStudents = data?.map(s => ({
      id: s.student_id,
      student_identifier: s.student_identifier,
      name: s.student_name,
      grade: s.grade,
      class: s.class,
      school_id: s.school_id,
      schools: { name: s.school_name }
    })) || [];

    res.json(formattedStudents);
  } catch (error) {
    console.error('获取教师学生数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 获取教师可管理的班级
 */
async function getTeacherClasses(req, res) {
  try {
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (isAdmin) {
      // 管理员可以看到所有班级配置
      const { data, error } = await db.supabase
        .from('school_grade_configs')
        .select(`
          *,
          schools!inner(id, name)
        `)
        .order('grade', { ascending: true });

      if (error) throw error;
      res.json(data || []);
      return;
    }

    // 教师只能看到有权限的班级 - 优化查询
    const { data, error } = await db.supabase
      .from('teacher_class_permissions')
      .select(`
        id,
        school_id,
        grade,
        class,
        schools!inner(name)
      `)
      .eq('teacher_id', teacherId)
      .order('school_id, grade, class')
      .limit(100);

    if (error) {
      console.error('获取教师班级数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取班级数据失败',
        error: error.message
      });
    }

    res.json(data || []);
  } catch (error) {
    console.error('获取教师班级数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 添加学校（仅管理员或有权限的教师）
 */
async function addTeacherSchool(req, res) {
  try {
    const { name, address, phone, contact_phone } = req.body;
    // 兼容两种字段名
    const phoneNumber = phone || contact_phone;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (!name || !name.trim()) {
      return res.status(400).json({
        success: false,
        message: '学校名称不能为空'
      });
    }

    // 检查学校名称是否已存在
    const { data: existingSchool } = await db.supabase
      .from('schools')
      .select('name')
      .eq('name', name.trim())
      .single();

    if (existingSchool) {
      return res.status(400).json({
        success: false,
        message: '学校名称已存在'
      });
    }

    // 创建学校
    console.log('准备创建学校:', {
      name: name.trim(),
      address: address?.trim() || null,
      contact_phone: phoneNumber?.trim() || null,
      teacherId,
      isAdmin
    });

    const { data: newSchool, error: schoolError } = await db.supabase
      .from('schools')
      .insert({
        name: name.trim(),
        address: address?.trim() || null,
        contact_phone: phoneNumber?.trim() || null,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (schoolError) {
      console.error('创建学校失败:', {
        error: schoolError,
        code: schoolError.code,
        message: schoolError.message,
        details: schoolError.details,
        hint: schoolError.hint
      });

      // 根据错误类型返回更具体的错误信息
      let errorMessage = '创建学校失败';
      if (schoolError.code === '23505') {
        errorMessage = '学校名称已存在';
      } else if (schoolError.code === '42501') {
        errorMessage = '权限不足，无法创建学校';
      } else if (schoolError.message) {
        errorMessage = schoolError.message;
      }

      return res.status(500).json({
        success: false,
        message: errorMessage,
        error: schoolError.message,
        code: schoolError.code
      });
    }

    console.log('学校创建成功:', newSchool);

    // 为创建学校的教师分配权限（如果不是管理员）
    if (!isAdmin) {
      console.log('为教师分配学校权限:', {
        teacherId,
        schoolId: newSchool.id
      });

      const { error: assignError } = await db.supabase
        .from('teacher_school_assignments')
        .insert({
          teacher_id: teacherId,
          school_id: newSchool.id,
          created_at: new Date().toISOString()
        });

      if (assignError) {
        console.error('分配学校权限失败:', {
          error: assignError,
          teacherId,
          schoolId: newSchool.id
        });

        // 权限分配失败，但学校已创建，返回警告而不是错误
        return res.status(201).json({
          success: true,
          message: '学校创建成功，但权限分配失败',
          data: newSchool,
          warning: '请联系管理员手动分配权限'
        });
      } else {
        console.log('学校权限分配成功');
      }
    }

    res.status(201).json({
      success: true,
      message: '学校创建成功',
      data: newSchool
    });
  } catch (error) {
    console.error('添加学校失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 更新学校信息
 */
async function updateTeacherSchool(req, res) {
  try {
    const schoolId = req.params.id;
    const { name, address, phone } = req.body;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    // 检查权限
    if (!isAdmin) {
      const hasPermission = await checkTeacherSchoolPermission(teacherId, schoolId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限修改此学校'
        });
      }
    }

    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (name) updateData.name = name.trim();
    if (address !== undefined) updateData.address = address?.trim() || null;
    if (phone !== undefined) updateData.contact_phone = phone?.trim() || null;

    const { data, error } = await db.supabase
      .from('schools')
      .update(updateData)
      .eq('id', schoolId)
      .select()
      .single();

    if (error) {
      console.error('更新学校失败:', error);
      return res.status(500).json({
        success: false,
        message: '更新学校失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: '学校信息更新成功',
      data
    });
  } catch (error) {
    console.error('更新学校失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 删除学校
 */
async function deleteTeacherSchool(req, res) {
  try {
    const schoolId = req.params.id;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    // 检查权限
    if (!isAdmin) {
      const hasPermission = await checkTeacherSchoolPermission(teacherId, schoolId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限删除此学校'
        });
      }
    }

    // 检查是否有学生关联到这个学校
    const { data: students } = await db.supabase
      .from('students')
      .select('id')
      .eq('school_id', schoolId)
      .limit(1);

    if (students && students.length > 0) {
      return res.status(400).json({
        success: false,
        message: '无法删除学校，还有学生关联到此学校'
      });
    }

    const { data, error } = await db.supabase
      .from('schools')
      .delete()
      .eq('id', schoolId)
      .select()
      .single();

    if (error) {
      console.error('删除学校失败:', error);
      return res.status(500).json({
        success: false,
        message: '删除学校失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: '学校删除成功',
      data
    });
  } catch (error) {
    console.error('删除学校失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 添加年级配置（设置年级班级数量）
 */
async function addGradeConfig(req, res) {
  try {
    const { school_id, grade, class_count, notes } = req.body;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (!school_id || !grade || !class_count) {
      return res.status(400).json({
        success: false,
        message: '学校ID、年级和班级数量不能为空'
      });
    }

    // 检查权限
    if (!isAdmin) {
      const hasPermission = await checkTeacherSchoolPermission(teacherId, school_id);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限在此学校添加年级配置'
        });
      }
    }

    // 检查年级配置是否已存在
    const { data: existingConfig } = await db.supabase
      .from('school_grade_configs')
      .select('*')
      .eq('school_id', school_id)
      .eq('grade', grade)
      .single();

    if (existingConfig) {
      return res.status(400).json({
        success: false,
        message: '该年级配置已存在'
      });
    }

    // 创建年级配置
    const { data: gradeConfig, error: configError } = await db.supabase
      .from('school_grade_configs')
      .insert({
        school_id,
        grade,
        class_count,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (configError) {
      console.error('创建年级配置失败:', configError);
      return res.status(500).json({
        success: false,
        message: '创建年级配置失败',
        error: configError.message
      });
    }

    console.log('年级配置创建成功:', gradeConfig);

    res.status(201).json({
      success: true,
      message: '年级配置创建成功',
      data: gradeConfig
    });
  } catch (error) {
    console.error('添加年级配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 添加年级（在指定学校下）
 */
async function addGrade(req, res) {
  try {
    const { school_id, grade, class: classNum } = req.body;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (!school_id || !grade || !classNum) {
      return res.status(400).json({
        success: false,
        message: '学校ID、年级和班级不能为空'
      });
    }

    // 检查权限
    if (!isAdmin) {
      const hasPermission = await checkTeacherSchoolPermission(teacherId, school_id);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限在此学校添加班级'
        });
      }
    }

    // 检查班级是否已存在
    const { data: existingClass } = await db.supabase
      .from('teacher_class_permissions')
      .select('*')
      .eq('school_id', school_id)
      .eq('grade', grade)
      .eq('class', classNum)
      .single();

    if (existingClass) {
      return res.status(400).json({
        success: false,
        message: '该班级已存在'
      });
    }

    // 首先确保教师有学校分配权限
    if (!isAdmin) {
      const assignmentResult = await ensureTeacherSchoolAssignment(teacherId, school_id);
      if (!assignmentResult.success) {
        return res.status(500).json({
          success: false,
          message: '创建学校分配失败',
          error: assignmentResult.error
        });
      }
    }

    // 创建班级记录（这里我们使用teacher_class_permissions表来记录班级存在）
    const { data, error } = await db.supabase
      .from('teacher_class_permissions')
      .insert({
        teacher_id: teacherId,
        school_id,
        grade,
        class: classNum,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('添加年级失败:', error);
      return res.status(500).json({
        success: false,
        message: error.code === '23505' ? '该年级已存在' : '添加年级失败',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: '年级添加成功',
      data
    });
  } catch (error) {
    console.error('添加年级失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 添加班级权限（为教师分配特定班级的管理权限）
 */
async function addClassPermission(req, res) {
  try {
    const { school_id, grade, class: classNum } = req.body;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (!school_id || !grade || !classNum) {
      return res.status(400).json({
        success: false,
        message: '学校ID、年级和班级不能为空'
      });
    }

    // 检查并确保学校权限
    if (!isAdmin) {
      const assignmentResult = await ensureTeacherSchoolAssignment(teacherId, school_id);
      if (!assignmentResult.success) {
        return res.status(403).json({
          success: false,
          message: '您没有权限访问此学校，且无法自动分配权限',
          error: assignmentResult.error
        });
      }
    }

    const { data, error } = await db.supabase
      .from('teacher_class_permissions')
      .insert({
        teacher_id: teacherId,
        school_id,
        grade,
        class: classNum,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('添加班级权限失败:', error);
      return res.status(500).json({
        success: false,
        message: error.code === '23505' ? '班级权限已存在' : '添加班级权限失败',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: '班级权限添加成功',
      data
    });
  } catch (error) {
    console.error('添加班级权限失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 删除班级权限
 */
async function deleteClassPermission(req, res) {
  try {
    const permissionId = req.params.id;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';



    // 查询班级权限信息，包含班级详细信息用于级联删除
    const { data: permission, error: fetchError } = await db.supabase
      .from('teacher_class_permissions')
      .select('id, teacher_id, school_id, grade, class')
      .eq('id', permissionId)
      .single();

    if (fetchError || !permission) {

      return res.status(404).json({
        success: false,
        message: `班级权限不存在 (ID: ${permissionId})`,
        details: '该班级权限记录可能已被删除或您没有访问权限'
      });
    }

    // 检查权限：只有管理员或权限所有者可以删除
    if (!isAdmin && permission.teacher_id !== teacherId) {
      return res.status(403).json({
        success: false,
        message: '您没有权限删除此班级权限'
      });
    }

    // 级联删除：先删除该班级的所有学生
    const { data: studentsToDelete, error: studentsError } = await db.supabase
      .from('students')
      .select('id, name')
      .eq('school_id', permission.school_id)
      .eq('grade', permission.grade)
      .eq('class', permission.class);

    if (studentsError) {
      console.error('查询班级学生失败:', studentsError);
      return res.status(500).json({
        success: false,
        message: '查询班级学生失败'
      });
    }

    // 如果有学生，先删除学生数据
    if (studentsToDelete && studentsToDelete.length > 0) {
      console.log(`准备级联删除 ${studentsToDelete.length} 名学生:`, studentsToDelete.map(s => s.name));

      const { error: deleteStudentsError } = await db.supabase
        .from('students')
        .delete()
        .eq('school_id', permission.school_id)
        .eq('grade', permission.grade)
        .eq('class', permission.class);

      if (deleteStudentsError) {
        console.error('级联删除学生失败:', deleteStudentsError);
        return res.status(500).json({
          success: false,
          message: '级联删除学生失败'
        });
      }
    }

    // 删除班级权限
    const { data, error } = await db.supabase
      .from('teacher_class_permissions')
      .delete()
      .eq('id', permissionId)
      .select()
      .single();

    if (error) {
      console.error('删除班级权限失败:', error);
      return res.status(500).json({
        success: false,
        message: '删除班级权限失败'
      });
    }

    // 清理相关缓存
    cache.delete(`schools_${teacherId}`);
    if (isAdmin) {
      cache.delete('schools_admin');
    }

    // 构建成功消息
    let successMessage = '班级权限删除成功';
    if (studentsToDelete && studentsToDelete.length > 0) {
      successMessage += `，同时删除了 ${studentsToDelete.length} 名学生数据`;
    }

    res.json({
      success: true,
      message: successMessage,
      deletedStudents: studentsToDelete ? studentsToDelete.length : 0
    });
  } catch (error) {
    console.error('删除班级权限失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

module.exports = {
  getTeacherSchools,
  getTeacherStudents,
  getTeacherClasses,
  addTeacherSchool,
  updateTeacherSchool,
  deleteTeacherSchool,
  addGradeConfig,
  addGrade,
  addClassPermission,
  deleteClassPermission,
  checkTeacherSchoolPermission,
  checkTeacherClassPermission,
  ensureTeacherSchoolAssignment
};