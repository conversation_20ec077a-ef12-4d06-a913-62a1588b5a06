-- 🔒 Supabase 视图安全策略配置脚本
-- 解决 teacher_permissions_view、teacher_visible_students、student_best_typing_records 的安全策略问题

-- ==========================================
-- 第一步：为视图启用行级安全 (RLS)
-- ==========================================

-- 注意：在 Supabase 中，视图也需要启用 RLS 并设置安全策略
ALTER VIEW teacher_permissions_view ENABLE ROW LEVEL SECURITY;
ALTER VIEW teacher_visible_students ENABLE ROW LEVEL SECURITY;
ALTER VIEW student_best_typing_records ENABLE ROW LEVEL SECURITY;

-- ==========================================
-- 第二步：创建视图的安全策略
-- ==========================================

-- 1. teacher_permissions_view 安全策略
-- 只允许认证用户查看，且教师只能查看自己的权限信息
CREATE POLICY "教师权限视图查看策略" ON teacher_permissions_view
    FOR SELECT USING (
        auth.role() = 'authenticated' AND (
            -- 管理员可以查看所有权限
            EXISTS (
                SELECT 1 FROM users
                WHERE users.id = auth.uid()::text::integer
                AND users.role = 'admin'
            )
            OR
            -- 教师只能查看自己的权限
            teacher_id = auth.uid()::text::integer
        )
    );

-- 2. teacher_visible_students 安全策略
-- 只允许认证用户查看，且教师只能查看自己有权限的学生
CREATE POLICY "教师可见学生视图查看策略" ON teacher_visible_students
    FOR SELECT USING (
        auth.role() = 'authenticated' AND (
            -- 管理员可以查看所有学生
            EXISTS (
                SELECT 1 FROM users
                WHERE users.id = auth.uid()::text::integer
                AND users.role = 'admin'
            )
            OR
            -- 教师只能查看自己有权限的学生
            teacher_id = auth.uid()::text::integer
        )
    );

-- 3. student_best_typing_records 安全策略
-- 只允许认证用户查看学生的最佳打字记录
CREATE POLICY "学生最佳打字记录视图查看策略" ON student_best_typing_records
    FOR SELECT USING (
        auth.role() = 'authenticated' AND (
            -- 管理员可以查看所有记录
            EXISTS (
                SELECT 1 FROM users
                WHERE users.id = auth.uid()::text::integer
                AND users.role = 'admin'
            )
            OR
            -- 教师只能查看自己有权限的学生的记录
            EXISTS (
                SELECT 1 FROM teacher_class_permissions tcp
                WHERE tcp.teacher_id = auth.uid()::text::integer
                AND tcp.school_id = student_best_typing_records.school_id
                AND tcp.grade = student_best_typing_records.grade
                AND tcp.class = student_best_typing_records.class
            )
        )
    );

-- ==========================================
-- 第三步：验证安全策略设置
-- ==========================================

-- 检查视图的 RLS 状态
SELECT 
    schemaname,
    viewname as tablename,
    'VIEW' as object_type
FROM pg_views 
WHERE schemaname = 'public' 
AND viewname IN ('teacher_permissions_view', 'teacher_visible_students', 'student_best_typing_records');

-- 检查视图的安全策略
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd as "操作类型",
    qual as "策略条件"
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename IN ('teacher_permissions_view', 'teacher_visible_students', 'student_best_typing_records');

-- ==========================================
-- 第四步：测试查询（可选）
-- ==========================================

-- 测试教师权限视图访问（需要认证）
-- SELECT COUNT(*) FROM teacher_permissions_view;

-- 测试教师可见学生视图访问（需要认证）
-- SELECT COUNT(*) FROM teacher_visible_students;

-- 测试学生最佳打字记录视图访问（需要认证）
-- SELECT COUNT(*) FROM student_best_typing_records;

-- ==========================================
-- 完成提示
-- ==========================================

SELECT
    '🔒 视图安全策略配置已完成' as 状态,
    '三个视图的 RLS 和安全策略已设置' as 详情,
    '请在 Supabase 控制台测试应用功能' as 下一步;
